import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { APP_TYPE } from '../../utils/constants'
import getConfig from 'next/config'

export default function Home() {
  return (
    <>
      <MainFrame app={APP_TYPE.dokAssessment} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['teaching-slides', 'common'])),
    },
  }
}
